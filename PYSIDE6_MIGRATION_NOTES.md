# PySide6 遷移說明

## 概述

本專案已完全遷移至使用 PySide6 作為 GUI 框架，移除了所有 PyQt 相關的依賴和引用。

## 修改內容

### 1. 主程式修改 (main.py)
- 在程式開始時設置環境變數 `QT_API=pyside6`，強制 pyqtgraph 使用 PySide6 後端
- 確保在任何 Qt 相關模組導入之前就完成配置

### 2. 繪圖模組修改 (plotter.py)
- 移除 `from pyqtgraph.Qt import QtWidgets` 的導入
- 改為直接從 PySide6.QtWidgets 導入所需的組件：
  - QWidget
  - QVBoxLayout
  - QHBoxLayout
  - QRadioButton
  - QButtonGroup
  - QCheckBox
  - QLabel
  - QDialog
- 替換所有 `QtWidgets.XXX()` 為對應的 PySide6 組件

### 3. PyInstaller 配置修改 (main.spec)
- 在 excludes 列表中添加 PyQt5、PyQt6、PyQt4，防止意外打包

### 4. 文檔更新 (README.md)
- 在安裝說明中添加重要提醒，說明專案完全使用 PySide6
- 強調 pyqtgraph 會自動配置使用 PySide6 作為後端

### 5. 清理工作
- 刪除舊的 build 和 dist 目錄，移除包含 PyQt 引用的編譯檔案
- 確保所有 Python 檔案都只使用 PySide6

## 技術細節

### pyqtgraph 後端配置
pyqtgraph 是一個支援多種 Qt 後端的繪圖庫，包括：
- PyQt4
- PyQt5
- PyQt6
- PySide2
- PySide6

通過在程式開始時設置 `os.environ['QT_API'] = 'pyside6'`，可以強制 pyqtgraph 使用 PySide6 作為後端，避免自動選擇其他 Qt 實現。

### 相容性確保
所有 UI 組件都已確認使用 PySide6：
- ✅ main.py
- ✅ app_pyside6.py
- ✅ plotter.py
- ✅ modern_widgets.py
- ✅ modern_styles.py
- ✅ data_analysis_tab.py
- ✅ comparison_tab.py
- ✅ trend_analysis_tab.py
- ✅ test_tabs_ui.py
- ✅ data_forwarder.py (僅使用 PySide6.QtCore)

### 驗證方法
可以通過以下方式驗證專案是否正確使用 PySide6：

1. 檢查程式啟動時的環境變數設置
2. 確認所有 import 語句都來自 PySide6
3. 運行程式時檢查是否有 PyQt 相關的錯誤或警告

## 注意事項

1. **環境變數設置**：必須在任何 Qt 相關模組導入之前設置 `QT_API=pyside6`
2. **依賴安裝**：確保安裝了 PySide6 而不是 PyQt
3. **編譯打包**：重新編譯時會自動排除 PyQt 相關模組
4. **向後相容**：此修改不影響程式的功能，只是更換了底層的 Qt 實現

## 圖表顯示問題修復

### 發現的問題
在遷移過程中發現了圖表顯示的問題：
1. 右軸線條（歐拉角）的可見性控制不完整
2. `update_plot` 方法中的可見性邏輯有誤
3. `clear` 方法沒有清除右軸線條數據

### 修復內容
1. **修復可見性控制**：
   - 在 `set_curve_visibility` 方法中添加對右軸線條的處理
   - 在 `_on_legend_checkbox_changed` 方法中添加對右軸線條的處理

2. **修復數據更新邏輯**：
   - 修改 `update_plot` 方法，確保所有線條都更新數據，但只有可見的線條影響Y軸範圍
   - 添加調試信息來幫助診斷問題

3. **修復清除功能**：
   - 在 `clear` 方法中添加對右軸線條的清除

### 測試工具
創建了以下測試工具來驗證修復：
- `test_pyqtgraph_basic.py`：基本的 pyqtgraph 功能測試
- `test_plotter.py`：完整的 plotter 功能測試
- `diagnose_plotter.py`：詳細的診斷工具

### 使用測試工具
```bash
# 基本測試
python test_pyqtgraph_basic.py

# 完整測試
python test_plotter.py

# 診斷工具
python diagnose_plotter.py
```

## 未來維護

在添加新的 UI 組件時，請確保：
1. 只從 PySide6 導入 Qt 相關模組
2. 不要使用 pyqtgraph.Qt 的組件
3. 保持環境變數設置在程式開始處
4. 對於雙Y軸的圖表，確保左軸和右軸的線條都正確處理可見性
