#!/usr/bin/env python3
# test_plotter.py - 測試 plotter 功能的簡單腳本

import os
import sys
import time
import math

# 強制 pyqtgraph 使用 PySide6 後端
os.environ['QT_API'] = 'pyside6'

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import QTimer
from plotter import Plotter

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Plotter 測試")
        self.setGeometry(100, 100, 800, 600)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 創建plotter
        self.plotter = Plotter()
        layout.addWidget(self.plotter.get_canvas())
        
        # 設置測試數據生成器
        self.timer = QTimer()
        self.timer.timeout.connect(self.generate_test_data)
        self.timer.start(50)  # 每50ms生成一次數據
        
        self.time_counter = 0
        
    def generate_test_data(self):
        """生成測試數據"""
        self.time_counter += 0.05  # 50ms = 0.05s
        
        # 生成模擬的IMU數據
        test_data = {
            'ax': int(math.sin(self.time_counter) * 16384),  # 模擬加速度
            'ay': int(math.cos(self.time_counter) * 16384),
            'az': int(math.sin(self.time_counter * 2) * 16384),
            'gx': int(math.sin(self.time_counter * 0.5) * 16384),  # 模擬角速度
            'gy': int(math.cos(self.time_counter * 0.5) * 16384),
            'gz': int(math.sin(self.time_counter * 0.3) * 16384),
            'roll': int(math.sin(self.time_counter * 0.2) * 16384),  # 模擬歐拉角
            'pitch': int(math.cos(self.time_counter * 0.2) * 16384),
            'yaw': int(math.sin(self.time_counter * 0.1) * 16384),
        }
        
        print(f"[TEST] 生成測試數據: ax={test_data['ax']}, ay={test_data['ay']}, az={test_data['az']}")
        
        # 添加數據到plotter
        self.plotter.add_data(test_data)
        self.plotter.update_plot()

def main():
    """測試plotter功能"""
    app = QApplication(sys.argv)
    
    window = TestWindow()
    window.show()
    
    print("開始測試 plotter...")
    print("應該看到三條加速度曲線（ax, ay, az）")
    print("可以點擊單選按鈕切換到角速度或歐拉角模式")
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
