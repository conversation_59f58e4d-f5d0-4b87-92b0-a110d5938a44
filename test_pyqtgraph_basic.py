#!/usr/bin/env python3
# test_pyqtgraph_basic.py - 基本的 pyqtgraph 測試

import os
import sys
import numpy as np

# 強制 pyqtgraph 使用 PySide6 後端
os.environ['QT_API'] = 'pyside6'

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import QTimer
import pyqtgraph as pg

class BasicTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PyQtGraph 基本測試")
        self.setGeometry(100, 100, 800, 600)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 創建基本的 PlotWidget
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.setBackground('w')
        self.plot_widget.showGrid(x=True, y=True)
        self.plot_widget.setTitle('基本測試', color='k', size='12pt')
        self.plot_widget.setLabel('left', 'Value', color='k')
        self.plot_widget.setLabel('bottom', 'Time', color='k')
        layout.addWidget(self.plot_widget)
        
        # 創建數據線條
        self.line1 = self.plot_widget.plot([], [], pen=pg.mkPen(color='red', width=2), name='Line 1')
        self.line2 = self.plot_widget.plot([], [], pen=pg.mkPen(color='blue', width=2), name='Line 2')
        
        # 設置數據
        self.x_data = []
        self.y1_data = []
        self.y2_data = []
        self.time_counter = 0
        
        # 設置定時器
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_data)
        self.timer.start(100)  # 每100ms更新一次
        
        print("基本 PyQtGraph 測試開始...")
        print("應該看到兩條線：紅色正弦波和藍色餘弦波")
        
    def update_data(self):
        """更新數據"""
        self.time_counter += 0.1
        
        # 生成測試數據
        self.x_data.append(self.time_counter)
        self.y1_data.append(np.sin(self.time_counter))
        self.y2_data.append(np.cos(self.time_counter))
        
        # 保持最近100個點
        if len(self.x_data) > 100:
            self.x_data = self.x_data[-100:]
            self.y1_data = self.y1_data[-100:]
            self.y2_data = self.y2_data[-100:]
        
        # 更新線條數據
        self.line1.setData(self.x_data, self.y1_data)
        self.line2.setData(self.x_data, self.y2_data)
        
        print(f"[TEST] 時間: {self.time_counter:.1f}, 數據點數: {len(self.x_data)}")

def main():
    """基本測試"""
    app = QApplication(sys.argv)
    
    window = BasicTestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
