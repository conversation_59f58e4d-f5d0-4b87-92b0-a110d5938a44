(['C:\\UCM\\Python\\6DIMU_sensor_tools\\main.py'],
 ['C:\\UCM\\Python\\6DIMU_sensor_tools'],
 [],
 [('C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_pyqtgraph_multiprocess',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pyqtgraph_multiprocess.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('main', 'C:\\UCM\\Python\\6DIMU_sensor_tools\\main.py', 'PYSOURCE')],
 [('_pyi_rth_utils.tempfile',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\struct.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\typing.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\contextlib.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\string.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\copy.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\numbers.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\inspect.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\argparse.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\gettext.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\token.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\opcode.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ast.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\bisect.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_strptime.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\datetime.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\calendar.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\getopt.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\selectors.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\quopri.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\textwrap.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\zipfile.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\py_compile.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\lzma.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_compression.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\bz2.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pathlib.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\csv.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tokenize.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tempfile.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\gzip.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\subprocess.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\signal.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\netrc.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\shlex.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ssl.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\client.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\hmac.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\queue.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\runpy.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\zipimport.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\stringprep.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('app_pyqt6',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\app_pyqt6.py',
   'PYMODULE'),
  ('serial_manager',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\serial_manager.py',
   'PYMODULE'),
  ('serial.tools.list_ports',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports.py',
   'PYMODULE'),
  ('serial.tools',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\serial\\tools\\__init__.py',
   'PYMODULE'),
  ('serial.tools.list_ports_common',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_common.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\glob.py',
   'PYMODULE'),
  ('serial.tools.list_ports_posix',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_posix.py',
   'PYMODULE'),
  ('serial.tools.list_ports_osx',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_osx.py',
   'PYMODULE'),
  ('serial.tools.list_ports_linux',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_linux.py',
   'PYMODULE'),
  ('serial.tools.list_ports_windows',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_windows.py',
   'PYMODULE'),
  ('serial.win32',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\serial\\win32.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\__future__.py',
   'PYMODULE'),
  ('serial',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\serial\\__init__.py',
   'PYMODULE'),
  ('serial.serialjava',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\serial\\serialjava.py',
   'PYMODULE'),
  ('serial.serialposix',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\serial\\serialposix.py',
   'PYMODULE'),
  ('serial.serialwin32',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\serial\\serialwin32.py',
   'PYMODULE'),
  ('serial.serialcli',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\serial\\serialcli.py',
   'PYMODULE'),
  ('serial.serialutil',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\serial\\serialutil.py',
   'PYMODULE'),
  ('plotter', 'C:\\UCM\\Python\\6DIMU_sensor_tools\\plotter.py', 'PYMODULE'),
  ('pyqtgraph.Qt.QtWidgets',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtWidgets\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.internals',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\internals.py',
   'PYMODULE'),
  ('numpy',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('fileinput',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\fileinput.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\doctest.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pdb.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\socketserver.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\tty.py',
   'PYMODULE'),
  ('readline',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\readline.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\smtplib.py',
   'PYMODULE'),
  ('pyreadline3.keysyms',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\codeop.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\bdb.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\cmd.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\difflib.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib._ctypeslib',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._format_impl',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('shiboken6',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\shiboken6\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.ui_file',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\ui_file.py',
   'PYMODULE'),
  ('PyQt6.uic.exceptions',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\exceptions.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('PyQt6.uic.objcreator',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\objcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.load_ui',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\load_ui.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader.loader',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Loader\\loader.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Loader\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.Loader.qobjectcreator',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Loader\\qobjectcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.uiparser',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\uiparser.py',
   'PYMODULE'),
  ('PyQt6.uic.properties',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\properties.py',
   'PYMODULE'),
  ('PyQt6.uic.icon_cache',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\icon_cache.py',
   'PYMODULE'),
  ('PyQt6.uic.enum_map',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\enum_map.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.compiler',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\compiler.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.qobjectcreator',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\qobjectcreator.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.as_string',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\as_string.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.indenter',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\indenter.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.qtproxies',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\qtproxies.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.proxy_metaclass',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\proxy_metaclass.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler.misc',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\misc.py',
   'PYMODULE'),
  ('PyQt6.uic.Compiler',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\Compiler\\__init__.py',
   'PYMODULE'),
  ('PyQt6.uic.compile_ui',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\compile_ui.py',
   'PYMODULE'),
  ('PyQt6',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.compat',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\compat\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.QtGui',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtGui\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.Qt.QtCore',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtCore\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\platform.py',
   'PYMODULE'),
  ('pyqtgraph',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.bootstrap',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\bootstrap.py',
   'PYMODULE'),
  ('pyqtgraph.imageview.ImageViewTemplate_generic',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\imageview\\ImageViewTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox.axisCtrlTemplate_generic',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\axisCtrlTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotItem.plotConfigTemplate_generic',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\plotConfigTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.FlowchartCtrlTemplate_generic',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\FlowchartCtrlTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Operators',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Operators.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.common',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\common.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.Node',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\Node.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.Terminal',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\Terminal.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Filters',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Filters.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Display',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Display.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.Data',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\Data.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.library.functions',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\library\\functions.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.NodeLibrary',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\NodeLibrary.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.Flowchart',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\Flowchart.py',
   'PYMODULE'),
  ('pyqtgraph.flowchart.FlowchartGraphicsView',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\flowchart\\FlowchartGraphicsView.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox.ViewBox',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\ViewBox.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox.ViewBoxMenu',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\ViewBoxMenu.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.DockArea',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\DockArea.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.DockDrop',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\DockDrop.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.Container',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\Container.py',
   'PYMODULE'),
  ('pyqtgraph.dockarea.Dock',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\dockarea\\Dock.py',
   'PYMODULE'),
  ('pyqtgraph.configfile',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\configfile.py',
   'PYMODULE'),
  ('pyqtgraph.units',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\units.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.TransformGuiTemplate_generic',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\TransformGuiTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.canvas',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.CanvasItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\CanvasItem.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.Canvas',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\Canvas.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.CanvasManager',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\CanvasManager.py',
   'PYMODULE'),
  ('pyqtgraph.canvas.CanvasTemplate_generic',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\canvas\\CanvasTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.exportDialogTemplate_generic',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\exportDialogTemplate_generic.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.ParameterTree',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterTree.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.ParameterItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterItem.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.text',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\text.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.str',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\str.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.slider',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\slider.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.qtenum',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\qtenum.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.progress',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\progress.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.pen',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\pen.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.PenPreviewLabel',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\PenPreviewLabel.py',
   'PYMODULE'),
  ('pyqtgraph.widgets',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.MatplotlibWidget',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\MatplotlibWidget.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.path',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('matplotlib._fontconfig_pattern',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_fontconfig_pattern.py',
   'PYMODULE'),
  ('pyparsing',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.common',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.core',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.util',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('matplotlib._afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_afm.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\plistlib.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib._enums',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\uuid.py',
   'PYMODULE'),
  ('matplotlib.typing',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\typing.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.container',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('contourpy',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\contourpy\\__init__.py',
   'PYMODULE'),
  ('contourpy.enum_util',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\contourpy\\enum_util.py',
   'PYMODULE'),
  ('contourpy.dechunk',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\contourpy\\dechunk.py',
   'PYMODULE'),
  ('contourpy.typecheck',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\contourpy\\typecheck.py',
   'PYMODULE'),
  ('contourpy.types',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\contourpy\\types.py',
   'PYMODULE'),
  ('contourpy.array',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\contourpy\\array.py',
   'PYMODULE'),
  ('contourpy.convert',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\contourpy\\convert.py',
   'PYMODULE'),
  ('contourpy.chunk',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\contourpy\\chunk.py',
   'PYMODULE'),
  ('contourpy._version',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\contourpy\\_version.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.table',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('dateutil.tz',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.easter',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil._common',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('six',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('dateutil',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._version',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('matplotlib.units',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\colorsys.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.colorizer',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\colorizer.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('matplotlib.style',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib._cm_bivar',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_cm_bivar.py',
   'PYMODULE'),
  ('matplotlib._cm_multivar',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_cm_multivar.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('cycler',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\cycler\\__init__.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends.registry',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\backends\\registry.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('matplotlib.text',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib._text_helpers',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib.layout_engine',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\layout_engine.py',
   'PYMODULE'),
  ('matplotlib._tight_layout',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_tight_layout.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib._layoutgrid',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE'),
  ('kiwisolver',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE'),
  ('kiwisolver.exceptions',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri._tritools',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_tritools.py',
   'PYMODULE'),
  ('matplotlib.tri._trirefine',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri._triplot',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_triplot.py',
   'PYMODULE'),
  ('matplotlib.tri._tripcolor',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri._triinterpolate',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri._trifinder',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_trifinder.py',
   'PYMODULE'),
  ('matplotlib.tri._tricontour',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_tricontour.py',
   'PYMODULE'),
  ('matplotlib.tri._triangulation',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\tri\\_triangulation.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.inset',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\inset.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.category',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.image',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\cbook.py',
   'PYMODULE'),
  ('matplotlib._api',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE'),
  ('matplotlib._api.deprecation',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits', '-', 'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib._docstring',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_docstring.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib._tight_bbox',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_tight_bbox.py',
   'PYMODULE'),
  ('matplotlib._blocking_input',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE'),
  ('matplotlib',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('matplotlib._version',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_qtagg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_qtagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_qt',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\backends\\backend_qt.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor.figureoptions',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\figureoptions.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor._formlayout',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\_formlayout.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_editor',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\backends\\qt_editor\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends.qt_compat',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\backends\\qt_compat.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.numeric',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\numeric.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.list',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\list.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.font',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\font.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.file',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\file.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.colormaplut',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\colormaplut.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorMapButton',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapButton.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.colormap',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\colormap.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.color',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\color.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.checklist',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\checklist.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.calendar',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\calendar.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.bool',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\bool.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.basetypes',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\basetypes.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.actiongroup',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\actiongroup.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.parameterTypes.action',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\parameterTypes\\action.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.Parameter',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\Parameter.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.interactive',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\interactive.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.ParameterSystem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\ParameterSystem.py',
   'PYMODULE'),
  ('pyqtgraph.parametertree.SystemSolver',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\parametertree\\SystemSolver.py',
   'PYMODULE'),
  ('pyqtgraph.console',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.console.Console',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\Console.py',
   'PYMODULE'),
  ('pyqtgraph.console.exception_widget',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\exception_widget.py',
   'PYMODULE'),
  ('pyqtgraph.console.stackwidget',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\stackwidget.py',
   'PYMODULE'),
  ('pyqtgraph.console.repl_widget',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\repl_widget.py',
   'PYMODULE'),
  ('pyqtgraph.console.CmdInput',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\console\\CmdInput.py',
   'PYMODULE'),
  ('pyqtgraph.exceptionHandling',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\exceptionHandling.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.VerticalLabel',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\VerticalLabel.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ValueLabel',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ValueLabel.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.TreeWidget',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\TreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.TableWidget',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\TableWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.SpinBox',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\SpinBox.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ScatterPlotWidget',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ScatterPlotWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.RemoteGraphicsView',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\RemoteGraphicsView.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.RawImageWidget',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\RawImageWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ProgressDialog',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ProgressDialog.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.PlotWidget',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\PlotWidget.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotItem.PlotItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\PlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.ImageExporter',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\ImageExporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.Exporter',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\Exporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.SVGExporter',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\SVGExporter.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.PathButton',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\PathButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.MultiPlotWidget',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\MultiPlotWidget.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.LayoutWidget',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\LayoutWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.JoystickButton',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\JoystickButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.HistogramLUTWidget',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\HistogramLUTWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GroupBox',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GroupBox.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GraphicsView',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GraphicsView.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GraphicsLayoutWidget',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GraphicsLayoutWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.GradientWidget',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\GradientWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.FileDialog',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\FileDialog.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.FeedbackButton',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\FeedbackButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.DiffTreeWidget',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\DiffTreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.DataTreeWidget',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\DataTreeWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.DataFilterWidget',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\DataFilterWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ComboBox',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ComboBox.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorMapWidget',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapWidget.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorMapMenu',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorMapMenu.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GradientPresets',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientPresets.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.ColorButton',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\ColorButton.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.CheckTable',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\CheckTable.py',
   'PYMODULE'),
  ('pyqtgraph.widgets.BusyCursor',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\widgets\\BusyCursor.py',
   'PYMODULE'),
  ('pyqtgraph.WidgetGroup',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\WidgetGroup.py',
   'PYMODULE'),
  ('pyqtgraph.Vector',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\Vector.py',
   'PYMODULE'),
  ('pyqtgraph.util.cupy_helper',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\cupy_helper.py',
   'PYMODULE'),
  ('pyqtgraph.util',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.util.cprint',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\cprint.py',
   'PYMODULE'),
  ('pyqtgraph.util.colorama.winterm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\winterm.py',
   'PYMODULE'),
  ('pyqtgraph.util.colorama',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.util.colorama.win32',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\colorama\\win32.py',
   'PYMODULE'),
  ('pyqtgraph.Transform3D',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\Transform3D.py',
   'PYMODULE'),
  ('pyqtgraph.ThreadsafeTimer',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\ThreadsafeTimer.py',
   'PYMODULE'),
  ('pyqtgraph.SignalProxy',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\SignalProxy.py',
   'PYMODULE'),
  ('pyqtgraph.Point',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\Point.py',
   'PYMODULE'),
  ('pyqtgraph.metaarray.MetaArray',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\metaarray\\MetaArray.py',
   'PYMODULE'),
  ('pyqtgraph.metaarray',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\metaarray\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.imageview',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\imageview\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.imageview.ImageView',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\imageview\\ImageView.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.GraphicsScene',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\GraphicsScene.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.exportDialog',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\exportDialog.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene.mouseEvents',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\mouseEvents.py',
   'PYMODULE'),
  ('pyqtgraph.GraphicsScene',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\GraphicsScene\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.VTickGroup',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\VTickGroup.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ViewBox',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ViewBox\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.UIGraphicsItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\UIGraphicsItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.TextItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\TextItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.TargetItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\TargetItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ScatterPlotItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ScatterPlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ScaleBar',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ScaleBar.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ROI',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ROI.py',
   'PYMODULE'),
  ('pyqtgraph.SRTTransform3D',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\SRTTransform3D.py',
   'PYMODULE'),
  ('pyqtgraph.SRTTransform',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\SRTTransform.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotItem\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotDataItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotDataItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PlotCurveItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PlotCurveItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.PColorMeshItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\PColorMeshItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.MultiPlotItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\MultiPlotItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.LinearRegionItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LinearRegionItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.LegendItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LegendItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.LabelItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\LabelItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ItemGroup',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ItemGroup.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.IsocurveItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\IsocurveItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.InfiniteLine',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\InfiniteLine.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ImageItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ImageItem.py',
   'PYMODULE'),
  ('pyqtgraph.functions_qimage',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\functions_qimage.py',
   'PYMODULE'),
  ('pyqtgraph.util.numba_helper',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\numba_helper.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.HistogramLUTItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\HistogramLUTItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GridItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GridItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsWidgetAnchor',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsWidgetAnchor.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsWidget',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsWidget.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsObject',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsObject.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsLayout',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsLayout.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GraphicsItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GraphicsItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GradientLegend',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientLegend.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.GradientEditorItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\GradientEditorItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.FillBetweenItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\FillBetweenItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ErrorBarItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ErrorBarItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.DateAxisItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\DateAxisItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.CurvePoint',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\CurvePoint.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ColorBarItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ColorBarItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ButtonItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ButtonItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.BarGraphItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\BarGraphItem.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.AxisItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\AxisItem.py',
   'PYMODULE'),
  ('pyqtgraph.exporters',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.PrintExporter',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\PrintExporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.Matplotlib',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\Matplotlib.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.HDF5Exporter',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\HDF5Exporter.py',
   'PYMODULE'),
  ('pyqtgraph.exporters.CSVExporter',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\exporters\\CSVExporter.py',
   'PYMODULE'),
  ('pyqtgraph.icons',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.graphicsItems.ArrowItem',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\graphicsItems\\ArrowItem.py',
   'PYMODULE'),
  ('pyqtgraph.functions',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\functions.py',
   'PYMODULE'),
  ('pyqtgraph.colormap',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colormap.py',
   'PYMODULE'),
  ('pyqtgraph.functions_numba',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\functions_numba.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\__init__.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.remoteproxy',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\remoteproxy.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.processes',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\processes.py',
   'PYMODULE'),
  ('pyqtgraph.multiprocess.parallelizer',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\multiprocess\\parallelizer.py',
   'PYMODULE'),
  ('pyqtgraph.reload',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\reload.py',
   'PYMODULE'),
  ('pyqtgraph.debug',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\debug.py',
   'PYMODULE'),
  ('pstats',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\pstats.py',
   'PYMODULE'),
  ('pyqtgraph.util.mutex',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\util\\mutex.py',
   'PYMODULE'),
  ('cProfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\cProfile.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\optparse.py',
   'PYMODULE'),
  ('profile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\profile.py',
   'PYMODULE'),
  ('pyqtgraph.colors.palette',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\palette.py',
   'PYMODULE'),
  ('pyqtgraph.colors',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\__init__.py',
   'PYMODULE')],
 [('python311.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python311.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('shiboken6\\Shiboken.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\shiboken6\\Shiboken.pyd',
   'EXTENSION'),
  ('PyQt6\\QtTest.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\QtTest.pyd',
   'EXTENSION'),
  ('PyQt6\\QtOpenGLWidgets.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\QtOpenGLWidgets.pyd',
   'EXTENSION'),
  ('PyQt6\\QtOpenGL.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\QtOpenGL.pyd',
   'EXTENSION'),
  ('PyQt6\\QtSvg.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\QtSvg.pyd',
   'EXTENSION'),
  ('PyQt6\\sip.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\sip.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtGui.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\QtGui.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('matplotlib\\_path.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_path.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\ft2font.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\ft2font.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('contourpy\\_contourpy.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\contourpy\\_contourpy.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\_webp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\_imagingtk.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\_imagingcms.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\_imagingmath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PIL\\_imaging.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_image.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_image.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\backends\\_backend_agg.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\backends\\_backend_agg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('kiwisolver\\_cext.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\kiwisolver\\_cext.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_tri.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_tri.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_qhull.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_qhull.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_c_internal_utils.cp311-win_amd64.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\_c_internal_utils.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtCore.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt6\\QtWidgets.pyd',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\QtWidgets.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-21\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Core.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Gui.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Svg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Pdf.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Network.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Widgets.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\shiboken6\\VCRUNTIME140.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python3.dll',
   'BINARY'),
  ('shiboken6\\shiboken6.abi3.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\shiboken6\\shiboken6.abi3.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140_1.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\shiboken6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6Test.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6Test.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6OpenGLWidgets.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6OpenGLWidgets.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\Qt6OpenGL.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\Qt6OpenGL.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_2.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\ucrtbase.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('shiboken6\\MSVCP140.dll',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\shiboken6\\MSVCP140.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-21\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-21\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-21\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-21\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\Java\\jdk-21\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-21\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-21\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\Java\\jdk-21\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files\\Java\\jdk-21\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('PyQt6\\uic\\widget-plugins\\qtopenglwidgets.py',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qtopenglwidgets.py',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qaxcontainer.py',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qaxcontainer.py',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qtprintsupport.py',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qtprintsupport.py',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qtquickwidgets.py',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qtquickwidgets.py',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qtcharts.py',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qtcharts.py',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qtwebenginewidgets.py',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qtwebenginewidgets.py',
   'DATA'),
  ('PyQt6\\uic\\widget-plugins\\qscintilla.py',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\uic\\widget-plugins\\qscintilla.py',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L14.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L14.csv',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee.svg',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L10.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L10.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D4.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D4.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D9.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D9.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L13.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L13.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D1A.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D1A.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D8.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D8.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTD1.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTD1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L6.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L6.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\PAL-relaxed_bright.hex',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\PAL-relaxed_bright.hex',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D3.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D3.csv',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee_128px.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee_128px.png',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C7.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C7.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C5.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C5.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L15.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L15.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\cividis.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\cividis.csv',
   'DATA'),
  ('pyqtgraph\\icons\\invisibleEye.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\invisibleEye.svg',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L5.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L5.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-I2.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-I2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\magma.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\magma.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L2.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L2.csv',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee_512px.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee_512px.png',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L3.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L3.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBL2.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBL2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CC-BY license - applies to CET color map data.txt',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CC-BY '
   'license - applies to CET color map data.txt',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L18.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L18.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D1.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D12.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D12.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D7.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D7.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-I3.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-I3.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D2.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C1s.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C1s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C3s.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C3s.csv',
   'DATA'),
  ('pyqtgraph\\icons\\lock.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\lock.png',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\inferno.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\inferno.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-R2.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-R2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-R1.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-R1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTL1.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTL1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTL2.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTL2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C1.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C6s.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C6s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBC1.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBC1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D11.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D11.csv',
   'DATA'),
  ('pyqtgraph\\icons\\auto.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\auto.png',
   'DATA'),
  ('pyqtgraph\\icons\\icons.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\icons.svg',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\<EMAIL>',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\<EMAIL>',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\PAL-relaxed.hex',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\PAL-relaxed.hex',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\plasma.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\plasma.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L19.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L19.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-R4.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-R4.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C4.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C4.csv',
   'DATA'),
  ('pyqtgraph\\Qt\\QtTest.pyi',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtTest.pyi',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C2s.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C2s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D10.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D10.csv',
   'DATA'),
  ('pyqtgraph\\icons\\default.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\default.png',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L7.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L7.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D13.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D13.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBL1.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBL1.csv',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee_256px.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee_256px.png',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L8.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L8.csv',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\<EMAIL>',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\<EMAIL>',
   'DATA'),
  ('pyqtgraph\\Qt\\__init__.pyi',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\__init__.pyi',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L4.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L4.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C4s.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C4s.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L1.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C3.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C3.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L12.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L12.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C7s.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C7s.csv',
   'DATA'),
  ('pyqtgraph\\icons\\ctrl.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\ctrl.png',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTC1.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTC1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBD1.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBD1.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-I1.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-I1.csv',
   'DATA'),
  ('pyqtgraph\\Qt\\QtWidgets\\__init__.pyi',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtWidgets\\__init__.pyi',
   'DATA'),
  ('pyqtgraph\\Qt\\QtGui\\__init__.pyi',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtGui\\__init__.pyi',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C6.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C6.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CC0 legal code - applies to virids, magma, '
   'plasma, inferno and cividis.txt',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CC0 '
   'legal code - applies to virids, magma, plasma, inferno and cividis.txt',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBTC2.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBTC2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L11.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L11.csv',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\<EMAIL>',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\<EMAIL>',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-R3.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-R3.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C5s.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C5s.csv',
   'DATA'),
  ('pyqtgraph\\Qt\\QtSvg.pyi',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtSvg.pyi',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L17.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L17.csv',
   'DATA'),
  ('pyqtgraph\\icons\\peegee\\peegee_192px.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\icons\\peegee\\peegee_192px.png',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L16.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L16.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-CBC2.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-CBC2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-C2.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-C2.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\viridis.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\viridis.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-L9.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-L9.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\CET-D6.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\CET-D6.csv',
   'DATA'),
  ('pyqtgraph\\colors\\maps\\turbo.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\colors\\maps\\turbo.csv',
   'DATA'),
  ('pyqtgraph\\Qt\\QtCore\\__init__.pyi',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyqtgraph\\Qt\\QtCore\\__init__.pyi',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\kpsewhich.lua',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\kpsewhich.lua',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.pdf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.pdf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave_large.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.pdf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.pdf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\matplotlibrc',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\matplotlibrc',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back_large.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\README.txt',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\README.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward_large.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.pdf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.pdf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help_large.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\goog.npz',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\goog.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.pdf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots_large.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home_large.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move_large.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\logo2.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\logo2.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\msft.csv',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\msft.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.pdf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_en.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hr.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_bg.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ko.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_es.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nl.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ca.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_da.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ka.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_uk.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fr.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fa.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ja.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gl.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_hu.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_gd.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_cs.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lv.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_de.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_he.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_tr.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sv.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_it.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_nn.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_nn.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_pl.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ar.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sk.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_lt.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_ru.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_fi.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qt_sl.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\PyQt6\\Qt6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\INSTALLER',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.3.1.dist-info\\entry_points.txt',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.3.1.dist-info\\DELVEWHEEL',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.3.1.dist-info\\METADATA',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\METADATA',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\METADATA',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.3.1.dist-info\\RECORD',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\RECORD',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\top_level.txt',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\top_level.txt',
   'DATA'),
  ('numpy-2.3.1.dist-info\\WHEEL',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.3.1.dist-info\\LICENSE.txt',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.3.1.dist-info\\INSTALLER',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\numpy-2.3.1.dist-info\\INSTALLER',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\RECORD',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\RECORD',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\WHEEL',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\.venv\\Lib\\site-packages\\pyreadline3-3.5.4.dist-info\\WHEEL',
   'DATA'),
  ('base_library.zip',
   'C:\\UCM\\Python\\6DIMU_sensor_tools\\build\\main\\base_library.zip',
   'DATA')],
 [('functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\functools.py',
   'PYMODULE'),
  ('collections.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('_collections_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('ntpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\ntpath.py',
   'PYMODULE'),
  ('codecs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\codecs.py',
   'PYMODULE'),
  ('copyreg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\copyreg.py',
   'PYMODULE'),
  ('re._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\re\\_parser.py',
   'PYMODULE'),
  ('re._constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('re',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\re\\__init__.py',
   'PYMODULE'),
  ('sre_constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('reprlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\reprlib.py',
   'PYMODULE'),
  ('genericpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\genericpath.py',
   'PYMODULE'),
  ('enum',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\enum.py',
   'PYMODULE'),
  ('traceback',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\traceback.py',
   'PYMODULE'),
  ('weakref',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\weakref.py',
   'PYMODULE'),
  ('abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\abc.py',
   'PYMODULE'),
  ('keyword',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\keyword.py',
   'PYMODULE'),
  ('os',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\os.py',
   'PYMODULE'),
  ('types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\types.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('operator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\operator.py',
   'PYMODULE'),
  ('posixpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\posixpath.py',
   'PYMODULE'),
  ('sre_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sre_compile.py',
   'PYMODULE'),
  ('locale',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\locale.py',
   'PYMODULE'),
  ('heapq',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\heapq.py',
   'PYMODULE'),
  ('warnings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\warnings.py',
   'PYMODULE'),
  ('_weakrefset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\_weakrefset.py',
   'PYMODULE'),
  ('sre_parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\sre_parse.py',
   'PYMODULE'),
  ('linecache',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\linecache.py',
   'PYMODULE'),
  ('io',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\io.py',
   'PYMODULE'),
  ('stat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\stat.py',
   'PYMODULE')])
