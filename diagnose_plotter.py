#!/usr/bin/env python3
# diagnose_plotter.py - 診斷 plotter 問題的腳本

import os
import sys

# 強制 pyqtgraph 使用 PySide6 後端
os.environ['QT_API'] = 'pyside6'

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PySide6.QtCore import QTimer
from plotter import Plotter

class DiagnosticWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Plotter 診斷工具")
        self.setGeometry(100, 100, 1000, 700)
        
        # 創建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 創建控制按鈕
        button_layout = QHBoxLayout()
        self.start_btn = QPushButton("開始數據生成")
        self.stop_btn = QPushButton("停止數據生成")
        self.check_btn = QPushButton("檢查狀態")
        self.clear_btn = QPushButton("清除數據")
        
        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addWidget(self.check_btn)
        button_layout.addWidget(self.clear_btn)
        main_layout.addLayout(button_layout)
        
        # 創建plotter
        self.plotter = Plotter()
        main_layout.addWidget(self.plotter.get_canvas())
        
        # 連接按鈕事件
        self.start_btn.clicked.connect(self.start_data_generation)
        self.stop_btn.clicked.connect(self.stop_data_generation)
        self.check_btn.clicked.connect(self.check_status)
        self.clear_btn.clicked.connect(self.clear_data)
        
        # 設置定時器
        self.timer = QTimer()
        self.timer.timeout.connect(self.generate_data)
        
        self.data_counter = 0
        
        print("=== Plotter 診斷工具 ===")
        print("1. 點擊 '開始數據生成' 開始測試")
        print("2. 點擊 '檢查狀態' 查看內部狀態")
        print("3. 嘗試切換不同的顯示模式（加速度/角速度/歐拉角）")
        
    def start_data_generation(self):
        """開始生成測試數據"""
        print("\n[診斷] 開始生成測試數據...")
        self.timer.start(100)  # 每100ms生成一次數據
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        
    def stop_data_generation(self):
        """停止生成測試數據"""
        print("\n[診斷] 停止生成測試數據")
        self.timer.stop()
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        
    def generate_data(self):
        """生成測試數據"""
        self.data_counter += 1
        
        # 生成簡單的測試數據
        test_data = {
            'ax': self.data_counter % 1000,  # 簡單的遞增數據
            'ay': (self.data_counter * 2) % 1000,
            'az': (self.data_counter * 3) % 1000,
            'gx': (self.data_counter * 4) % 1000,
            'gy': (self.data_counter * 5) % 1000,
            'gz': (self.data_counter * 6) % 1000,
            'roll': (self.data_counter * 7) % 1000,
            'pitch': (self.data_counter * 8) % 1000,
            'yaw': (self.data_counter * 9) % 1000,
        }
        
        if self.data_counter % 20 == 0:  # 每20次打印一次
            print(f"[診斷] 第{self.data_counter}次數據: ax={test_data['ax']}")
        
        # 添加數據到plotter
        self.plotter.add_data(test_data)
        self.plotter.update_plot()
        
    def check_status(self):
        """檢查plotter內部狀態"""
        print("\n=== Plotter 狀態檢查 ===")
        
        # 檢查數據緩衝區
        print("數據緩衝區狀態:")
        for key, buffer in self.plotter.data_buffers.items():
            print(f"  {key}: {len(buffer)} 個數據點")
            if len(buffer) > 0:
                print(f"    最新值: {buffer[-1]:.2f}")
        
        # 檢查可見性狀態
        print("\n可見性狀態:")
        for key, visible in self.plotter.visible_dict.items():
            print(f"  {key}: {'可見' if visible else '隱藏'}")
        
        # 檢查線條狀態
        print("\n線條狀態:")
        for key, line in self.plotter.lines.items():
            print(f"  {key}: 可見={line.isVisible()}")
        
        print("\n右軸線條狀態:")
        for key, line in self.plotter.right_lines.items():
            print(f"  {key}: 可見={line.isVisible()}")
        
        # 檢查模式狀態
        print(f"\n當前模式:")
        print(f"  加速度模式: {self.plotter.radio_acc.isChecked()}")
        print(f"  角速度模式: {self.plotter.radio_gyro.isChecked()}")
        print(f"  歐拉角模式: {self.plotter.radio_euler.isChecked()}")
        
    def clear_data(self):
        """清除所有數據"""
        print("\n[診斷] 清除所有數據")
        self.plotter.clear()
        self.data_counter = 0

def main():
    """診斷工具主函數"""
    app = QApplication(sys.argv)
    
    window = DiagnosticWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
